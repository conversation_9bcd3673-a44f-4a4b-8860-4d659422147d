
import AdminLayout from "@/components/AdminLayout";
import { Card } from "@/components/ui/card";
import { MessageSquare, Users, TrendingUp, Clock } from "lucide-react";

const AdminDashboard = () => {
  // Mock data - in a real app, this would come from an API
  const stats = [
    {
      title: "Total Messages",
      value: "24",
      change: "+3 this week",
      icon: MessageSquare,
      color: "text-blue-600",
      bgColor: "bg-blue-100"
    },
    {
      title: "Response Rate",
      value: "95%",
      change: "+5% this month",
      icon: TrendingUp,
      color: "text-green-600",
      bgColor: "bg-green-100"
    },
    {
      title: "Avg Response Time",
      value: "2.3h",
      change: "-30min this week",
      icon: Clock,
      color: "text-orange-600",
      bgColor: "bg-orange-100"
    },
    {
      title: "Active Users",
      value: "158",
      change: "+12 this month",
      icon: Users,
      color: "text-purple-600",
      bgColor: "bg-purple-100"
    }
  ];

  const recentMessages = [
    { id: 1, name: "<PERSON>", email: "<EMAIL>", message: "Interested in your services...", time: "2 hours ago" },
    { id: 2, name: "<PERSON>", email: "<EMAIL>", message: "Partnership opportunity...", time: "5 hours ago" },
    { id: 3, name: "Mike Johnson", email: "<EMAIL>", message: "Question about pricing...", time: "1 day ago" },
  ];

  return (
    <AdminLayout currentPage="Dashboard">
      <div className="space-y-6">
        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat, index) => (
            <Card key={index} className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                  <p className="text-2xl font-bold text-gray-900 mt-1">{stat.value}</p>
                  <p className="text-sm text-gray-500 mt-1">{stat.change}</p>
                </div>
                <div className={`p-3 rounded-lg ${stat.bgColor}`}>
                  <stat.icon className={`w-6 h-6 ${stat.color}`} />
                </div>
              </div>
            </Card>
          ))}
        </div>

        {/* Recent Messages */}
        <Card className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-800">Recent Messages</h3>
            <a href="/admin/messages" className="text-orange-600 hover:underline text-sm">
              View All
            </a>
          </div>
          
          <div className="space-y-4">
            {recentMessages.map((message) => (
              <div key={message.id} className="flex items-start space-x-4 p-4 border border-gray-200 rounded-lg">
                <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                  <span className="text-sm font-medium text-gray-600">
                    {message.name.split(' ').map(n => n[0]).join('')}
                  </span>
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium text-gray-900">{message.name}</p>
                    <p className="text-xs text-gray-500">{message.time}</p>
                  </div>
                  <p className="text-sm text-gray-600">{message.email}</p>
                  <p className="text-sm text-gray-800 mt-1 truncate">{message.message}</p>
                </div>
              </div>
            ))}
          </div>
        </Card>

        {/* Quick Actions */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Quick Actions</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <a
              href="/admin/messages"
              className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <MessageSquare className="w-8 h-8 text-orange-600 mb-2" />
              <h4 className="font-medium text-gray-900">Manage Messages</h4>
              <p className="text-sm text-gray-600">View and respond to inquiries</p>
            </a>
            
            <a
              href="/"
              className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <TrendingUp className="w-8 h-8 text-orange-600 mb-2" />
              <h4 className="font-medium text-gray-900">View Website</h4>
              <p className="text-sm text-gray-600">Check the public website</p>
            </a>
            
            <a
              href="/admin/settings"
              className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <Users className="w-8 h-8 text-orange-600 mb-2" />
              <h4 className="font-medium text-gray-900">Settings</h4>
              <p className="text-sm text-gray-600">Configure admin preferences</p>
            </a>
          </div>
        </Card>
      </div>
    </AdminLayout>
  );
};

export default AdminDashboard;
