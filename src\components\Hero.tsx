
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>, MapPin, Compass } from "lucide-react";

const Hero = () => {
  const scrollToContact = () => {
    const element = document.getElementById('contact');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const scrollToAbout = () => {
    const element = document.getElementById('about');
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section id="hero" className="pt-20 pb-16 min-h-screen flex items-center relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-20 left-10 transform rotate-12">
          <Compass size={100} className="text-orange-600" />
        </div>
        <div className="absolute top-40 right-20 transform -rotate-12">
          <MapPin size={80} className="text-red-600" />
        </div>
        <div className="absolute bottom-20 left-1/4 transform rotate-45">
          <Compass size={60} className="text-amber-600" />
        </div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-4xl mx-auto text-center">
          <div className="mb-8">
            <span className="inline-block px-4 py-2 bg-gradient-to-r from-orange-100 to-red-100 text-orange-800 rounded-full text-sm font-semibold mb-4 animate-pulse">
              🌍 Discover Southern Africa
            </span>
          </div>
          
          <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight">
            <span className="bg-gradient-to-r from-orange-600 via-red-600 to-amber-600 bg-clip-text text-transparent">
              South Safari
            </span>
            <br />
            <span className="text-gray-800 text-4xl md:text-5xl">
              Your Gateway to
            </span>
            <br />
            <span className="text-gray-700 text-3xl md:text-4xl">
              Digital Adventures
            </span>
          </h1>
          
          <p className="text-xl md:text-2xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
            Embark on a journey through Southern Africa's digital landscape. 
            We connect developers, entrepreneurs, and innovators to create 
            extraordinary experiences across the continent.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button 
              onClick={scrollToContact}
              size="lg"
              className="bg-gradient-to-r from-orange-500 to-red-600 hover:from-orange-600 hover:to-red-700 text-white px-8 py-4 rounded-full text-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl cursor-pointer"
            >
              Start Your Journey
              <ArrowRight className="ml-2" size={20} />
            </Button>
            
            <Button 
              variant="outline"
              size="lg"
              onClick={scrollToAbout}
              className="border-2 border-orange-500 text-orange-600 hover:bg-orange-500 hover:text-white px-8 py-4 rounded-full text-lg font-semibold transition-all duration-300 cursor-pointer"
            >
              Learn More
            </Button>
          </div>
          
          <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 max-w-3xl mx-auto">
            <div className="text-center">
              <div className="text-3xl font-bold text-orange-600 mb-2">100+</div>
              <div className="text-gray-600">Digital Projects</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-red-600 mb-2">15+</div>
              <div className="text-gray-600">Countries Reached</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-amber-600 mb-2">50+</div>
              <div className="text-gray-600">Happy Partners</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
