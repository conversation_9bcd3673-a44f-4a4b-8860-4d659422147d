
import { Card } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Code, Rocket, Users, BarChart3, Shield, Zap } from "lucide-react";

const Services = () => {
  const services = [
    {
      icon: <Code className="w-12 h-12 text-orange-600" />,
      title: "Digital Development",
      description: "Custom web and mobile applications built with cutting-edge technologies and African-inspired design.",
      features: ["React & Node.js", "Mobile Apps", "Cloud Solutions", "API Integration"]
    },
    {
      icon: <Rocket className="w-12 h-12 text-red-600" />,
      title: "Market Expansion",
      description: "Strategic guidance to help your digital products reach new markets across Southern Africa.",
      features: ["Market Research", "Launch Strategy", "Local Partnerships", "Growth Planning"]
    },
    {
      icon: <Users className="w-12 h-12 text-amber-600" />,
      title: "Developer Partnerships",
      description: "Connect with talented developers and create collaborative networks for innovative projects.",
      features: ["Talent Matching", "Team Building", "Skill Development", "Project Management"]
    },
    {
      icon: <BarChart3 className="w-12 h-12 text-orange-500" />,
      title: "Analytics & Insights",
      description: "Data-driven solutions to understand your audience and optimize your digital presence.",
      features: ["Performance Tracking", "User Analytics", "Market Intelligence", "ROI Analysis"]
    },
    {
      icon: <Shield className="w-12 h-12 text-red-500" />,
      title: "Security Solutions",
      description: "Robust security measures to protect your digital assets and user data across all platforms.",
      features: ["Data Protection", "Security Audits", "Compliance", "Risk Assessment"]
    },
    {
      icon: <Zap className="w-12 h-12 text-amber-500" />,
      title: "Innovation Labs",
      description: "Experimental spaces where ideas come to life through rapid prototyping and testing.",
      features: ["Prototype Development", "Innovation Workshops", "Tech Exploration", "Proof of Concept"]
    }
  ];

  const handleLearnMore = (serviceName: string) => {
    // Scroll to contact section when learn more is clicked
    const contactElement = document.getElementById('contact');
    if (contactElement) {
      contactElement.scrollIntoView({ behavior: 'smooth' });
      // Optional: Show a toast or alert about the specific service
      console.log(`Learning more about: ${serviceName}`);
    }
  };

  const scrollToContact = () => {
    const element = document.getElementById('contact');
    element?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <section id="services" className="py-20 bg-gradient-to-b from-gray-50 to-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            <span className="bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
              Our Services
            </span>
          </h2>
          <p className="text-xl text-gray-600 leading-relaxed">
            Comprehensive digital solutions tailored for the unique challenges and 
            opportunities of the Southern African market. From development to deployment, 
            we've got you covered.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <Card key={index} className="p-8 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border-orange-100 group">
              <div className="flex justify-center mb-6">
                <div className="p-4 bg-gradient-to-br from-orange-50 to-red-50 rounded-2xl group-hover:scale-110 transition-transform duration-300">
                  {service.icon}
                </div>
              </div>
              
              <h3 className="text-2xl font-bold mb-4 text-gray-800 text-center">
                {service.title}
              </h3>
              
              <p className="text-gray-600 mb-6 text-center leading-relaxed">
                {service.description}
              </p>
              
              <div className="space-y-2 mb-6">
                {service.features.map((feature, idx) => (
                  <div key={idx} className="flex items-center">
                    <div className="w-2 h-2 bg-gradient-to-r from-orange-500 to-red-500 rounded-full mr-3"></div>
                    <span className="text-gray-700 text-sm">{feature}</span>
                  </div>
                ))}
              </div>
              
              <Button 
                onClick={() => handleLearnMore(service.title)}
                variant="outline" 
                className="w-full border-orange-500 text-orange-600 hover:bg-orange-500 hover:text-white transition-all duration-300 cursor-pointer"
              >
                Learn More
              </Button>
            </Card>
          ))}
        </div>

        <div className="mt-16 text-center">
          <div className="bg-gradient-to-r from-orange-500 to-red-600 rounded-2xl p-8 md:p-12 text-white">
            <h3 className="text-3xl font-bold mb-4">Ready to Transform Your Digital Vision?</h3>
            <p className="text-xl mb-6 opacity-90">
              Let's discuss how we can help you achieve your goals in the Southern African market.
            </p>
            <Button 
              size="lg"
              variant="secondary"
              className="bg-white text-orange-600 hover:bg-gray-100 px-8 py-4 rounded-full text-lg font-semibold cursor-pointer"
              onClick={scrollToContact}
            >
              Get Started Today
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Services;
