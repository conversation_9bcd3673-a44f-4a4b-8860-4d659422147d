
import { useState, useEffect } from "react";
import AdminLayout from "@/components/AdminLayout";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { Reply, Search, Filter } from "lucide-react";

const AdminMessages = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedMessage, setSelectedMessage] = useState<any>(null);
  const [replyText, setReplyText] = useState("");
  const [filter, setFilter] = useState("all");
  const [messages, setMessages] = useState<any[]>([]);
  const { toast } = useToast();

  // Load messages from localStorage on component mount
  useEffect(() => {
    const loadMessages = () => {
      const storedMessages = localStorage.getItem('admin_messages');
      if (storedMessages) {
        try {
          const parsedMessages = JSON.parse(storedMessages);
          setMessages(parsedMessages);
        } catch (error) {
          console.error('Error parsing stored messages:', error);
          setMessages([]);
        }
      } else {
        // If no messages exist, initialize with empty array
        setMessages([]);
      }
    };

    loadMessages();

    // Set up interval to check for new messages every 5 seconds
    const interval = setInterval(loadMessages, 5000);
    
    return () => clearInterval(interval);
  }, []);

  const filteredMessages = messages.filter(message => {
    const matchesSearch = message.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         message.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         message.message.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesFilter = filter === "all" || message.status === filter;
    
    return matchesSearch && matchesFilter;
  });

  const handleReply = () => {
    if (!replyText.trim()) return;

    // In a real app, this would send an email
    console.log("Sending reply to:", selectedMessage.email);
    console.log("Reply:", replyText);

    // Update message status
    const updatedMessages = messages.map(msg => 
      msg.id === selectedMessage.id 
        ? { ...msg, status: "replied" }
        : msg
    );
    
    setMessages(updatedMessages);
    localStorage.setItem('admin_messages', JSON.stringify(updatedMessages));

    toast({
      title: "Reply Sent",
      description: `Your reply has been sent to ${selectedMessage.name}`,
    });

    setReplyText("");
    setSelectedMessage(null);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "unread":
        return "bg-red-100 text-red-800";
      case "replied":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <AdminLayout currentPage="Messages">
      <div className="space-y-6">
        {/* Header Actions */}
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          <div className="flex items-center space-x-4 flex-1">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search messages..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <div className="flex items-center space-x-2">
              <Filter className="w-4 h-4 text-gray-400" />
              <select
                value={filter}
                onChange={(e) => setFilter(e.target.value)}
                className="border border-gray-300 rounded-md px-3 py-2 text-sm"
              >
                <option value="all">All Messages</option>
                <option value="unread">Unread</option>
                <option value="replied">Replied</option>
              </select>
            </div>
          </div>
        </div>

        {/* Messages List */}
        <div className="space-y-4">
          {filteredMessages.map((message) => (
            <Card key={message.id} className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h3 className="font-semibold text-gray-900">{message.name}</h3>
                    <Badge className={getStatusColor(message.status)}>
                      {message.status}
                    </Badge>
                  </div>
                  
                  <div className="text-sm text-gray-600 mb-2">
                    <p>Email: {message.email}</p>
                    {message.company && <p>Company: {message.company}</p>}
                    <p>Date: {formatDate(message.createdAt)}</p>
                  </div>
                  
                  <p className="text-gray-800 mb-4">{message.message}</p>
                </div>
                
                <Dialog>
                  <DialogTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSelectedMessage(message)}
                      className="flex items-center space-x-2"
                    >
                      <Reply className="w-4 h-4" />
                      <span>Reply</span>
                    </Button>
                  </DialogTrigger>
                  
                  <DialogContent className="max-w-2xl">
                    <DialogHeader>
                      <DialogTitle>Reply to {message.name}</DialogTitle>
                    </DialogHeader>
                    
                    <div className="space-y-4">
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <p className="font-medium text-gray-900 mb-2">Original Message:</p>
                        <p className="text-gray-700 text-sm">{message.message}</p>
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Your Reply
                        </label>
                        <Textarea
                          value={replyText}
                          onChange={(e) => setReplyText(e.target.value)}
                          placeholder="Type your reply here..."
                          rows={6}
                          className="w-full"
                        />
                      </div>
                      
                      <div className="flex justify-end space-x-3">
                        <Button variant="outline" onClick={() => setSelectedMessage(null)}>
                          Cancel
                        </Button>
                        <Button 
                          onClick={handleReply}
                          className="bg-gradient-to-r from-orange-500 to-red-600 hover:from-orange-600 hover:to-red-700"
                        >
                          Send Reply
                        </Button>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
              </div>
            </Card>
          ))}
          
          {filteredMessages.length === 0 && (
            <Card className="p-8 text-center">
              <p className="text-gray-500">
                {messages.length === 0 
                  ? "No messages yet. Messages from the contact form will appear here." 
                  : "No messages match your search criteria."
                }
              </p>
            </Card>
          )}
        </div>
      </div>
    </AdminLayout>
  );
};

export default AdminMessages;
