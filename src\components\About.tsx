
import { Card } from "@/components/ui/card";
import { Target, Users, Globe, Lightbulb } from "lucide-react";

const About = () => {
  const values = [
    {
      icon: <Target className="w-8 h-8 text-orange-600" />,
      title: "Innovation",
      description: "We push boundaries and embrace cutting-edge technologies to create groundbreaking solutions."
    },
    {
      icon: <Users className="w-8 h-8 text-red-600" />,
      title: "Collaboration",
      description: "Building strong partnerships and fostering community connections across Southern Africa."
    },
    {
      icon: <Globe className="w-8 h-8 text-amber-600" />,
      title: "Impact",
      description: "Creating meaningful digital experiences that transform communities and drive growth."
    },
    {
      icon: <Lightbulb className="w-8 h-8 text-orange-500" />,
      title: "Excellence",
      description: "Delivering world-class solutions with attention to detail and commitment to quality."
    }
  ];

  return (
    <section id="about" className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            <span className="bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
              About South Safari
            </span>
          </h2>
          <p className="text-xl text-gray-600 leading-relaxed">
            Founded in the heart of Southern Africa, South Safari bridges the gap between 
            traditional landscapes and digital frontiers. We specialize in connecting 
            developers with unique opportunities, facilitating market expansion, and 
            creating innovative platforms that celebrate the rich diversity of our continent.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {values.map((value, index) => (
            <Card key={index} className="p-6 text-center hover:shadow-lg transition-all duration-300 transform hover:-translate-y-2 border-orange-100">
              <div className="flex justify-center mb-4">
                {value.icon}
              </div>
              <h3 className="text-xl font-semibold mb-3 text-gray-800">{value.title}</h3>
              <p className="text-gray-600">{value.description}</p>
            </Card>
          ))}
        </div>

        <div className="bg-gradient-to-r from-orange-50 to-red-50 rounded-2xl p-8 md:p-12">
          <div className="max-w-3xl mx-auto text-center">
            <h3 className="text-3xl font-bold mb-6 text-gray-800">Our Mission</h3>
            <p className="text-lg text-gray-700 leading-relaxed mb-6">
              To empower the next generation of digital innovators across Southern Africa 
              by providing world-class platforms, strategic partnerships, and cutting-edge 
              technology solutions that drive sustainable growth and meaningful impact.
            </p>
            <div className="flex flex-wrap justify-center gap-4">
              <span className="px-4 py-2 bg-orange-200 text-orange-800 rounded-full text-sm font-semibold">
                Digital Innovation
              </span>
              <span className="px-4 py-2 bg-red-200 text-red-800 rounded-full text-sm font-semibold">
                Community Building
              </span>
              <span className="px-4 py-2 bg-amber-200 text-amber-800 rounded-full text-sm font-semibold">
                Market Expansion
              </span>
              <span className="px-4 py-2 bg-orange-200 text-orange-800 rounded-full text-sm font-semibold">
                Strategic Partnerships
              </span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
