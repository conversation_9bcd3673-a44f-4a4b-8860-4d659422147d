
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Mail, Phone, MapPin, Send } from "lucide-react";
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";

const Contact = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    company: '',
    message: ''
  });
  const { toast } = useToast();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Create message object
    const newMessage = {
      id: Date.now(),
      ...formData,
      createdAt: new Date().toISOString(),
      status: 'unread'
    };
    
    // Store in localStorage (in production, this would be sent to a database)
    const existingMessages = JSON.parse(localStorage.getItem('admin_messages') || '[]');
    const updatedMessages = [newMessage, ...existingMessages];
    localStorage.setItem('admin_messages', JSON.stringify(updatedMessages));
    
    console.log('Contact form submitted:', formData);
    toast({
      title: "Message Sent!",
      description: "Thank you for contacting South Safari. We'll get back to you soon.",
    });
    setFormData({ name: '', email: '', company: '', message: '' });
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }));
  };

  return (
    <section id="contact" className="py-20 bg-gradient-to-b from-white to-orange-50">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            <span className="bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
              Get In Touch
            </span>
          </h2>
          <p className="text-xl text-gray-600 leading-relaxed">
            Ready to start your digital safari? We'd love to hear about your project 
            and explore how we can help you succeed in Southern Africa.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Contact Form */}
          <Card className="p-8 shadow-lg border-orange-100">
            <h3 className="text-2xl font-bold mb-6 text-gray-800">Send us a Message</h3>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Full Name *
                  </label>
                  <Input
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    required
                    placeholder="Your full name"
                    className="border-orange-200 focus:border-orange-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Email Address *
                  </label>
                  <Input
                    name="email"
                    type="email"
                    value={formData.email}
                    onChange={handleChange}
                    required
                    placeholder="<EMAIL>"
                    className="border-orange-200 focus:border-orange-500"
                  />
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Company (Optional)
                </label>
                <Input
                  name="company"
                  value={formData.company}
                  onChange={handleChange}
                  placeholder="Your company name"
                  className="border-orange-200 focus:border-orange-500"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Message *
                </label>
                <Textarea
                  name="message"
                  value={formData.message}
                  onChange={handleChange}
                  required
                  placeholder="Tell us about your project or how we can help..."
                  rows={6}
                  className="border-orange-200 focus:border-orange-500"
                />
              </div>
              
              <Button 
                type="submit" 
                className="w-full bg-gradient-to-r from-orange-500 to-red-600 hover:from-orange-600 hover:to-red-700 text-white py-3 rounded-full text-lg font-semibold transition-all duration-300 transform hover:scale-105"
              >
                Send Message
                <Send className="ml-2" size={18} />
              </Button>
            </form>
          </Card>

          {/* Contact Information */}
          <div className="space-y-8">
            <Card className="p-6 border-orange-100">
              <div className="flex items-start space-x-4">
                <div className="p-3 bg-gradient-to-br from-orange-100 to-red-100 rounded-lg">
                  <Mail className="w-6 h-6 text-orange-600" />
                </div>
                <div>
                  <h4 className="text-lg font-semibold text-gray-800 mb-2">Email Us</h4>
                  <p className="text-gray-600 mb-1"><EMAIL></p>
                  <p className="text-gray-600"><EMAIL></p>
                </div>
              </div>
            </Card>

            <Card className="p-6 border-orange-100">
              <div className="flex items-start space-x-4">
                <div className="p-3 bg-gradient-to-br from-orange-100 to-red-100 rounded-lg">
                  <Phone className="w-6 h-6 text-red-600" />
                </div>
                <div>
                  <h4 className="text-lg font-semibold text-gray-800 mb-2">Call Us</h4>
                  <p className="text-gray-600 mb-1">+27 (0) 11 123 4567</p>
                  <p className="text-gray-600">+27 (0) 21 987 6543</p>
                </div>
              </div>
            </Card>

            <Card className="p-6 border-orange-100">
              <div className="flex items-start space-x-4">
                <div className="p-3 bg-gradient-to-br from-orange-100 to-red-100 rounded-lg">
                  <MapPin className="w-6 h-6 text-amber-600" />
                </div>
                <div>
                  <h4 className="text-lg font-semibold text-gray-800 mb-2">Visit Us</h4>
                  <p className="text-gray-600 mb-1">Cape Town, South Africa</p>
                  <p className="text-gray-600">Johannesburg, South Africa</p>
                </div>
              </div>
            </Card>

            <div className="bg-gradient-to-r from-orange-500 to-red-600 rounded-2xl p-6 text-white">
              <h4 className="text-xl font-bold mb-3">Let's Build Something Amazing</h4>
              <p className="opacity-90 mb-4">
                Whether you're a startup looking to make your mark or an established 
                company seeking digital transformation, we're here to help you navigate 
                the exciting landscape of Southern African technology.
              </p>
              <div className="flex flex-wrap gap-2">
                <span className="px-3 py-1 bg-white/20 rounded-full text-sm">
                  Free Consultation
                </span>
                <span className="px-3 py-1 bg-white/20 rounded-full text-sm">
                  24/7 Support
                </span>
                <span className="px-3 py-1 bg-white/20 rounded-full text-sm">
                  Local Expertise
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Contact;
