import { MapPin, Mail, Phone } from "lucide-react";

const Footer = () => {
  return (
    <footer className="bg-white py-16 border-t border-orange-100">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Contact Info */}
          <div>
            <h3 className="text-lg font-bold text-gray-800 mb-4">Contact Us</h3>
            <p className="text-gray-600 mb-2 flex items-center">
              <MapPin className="mr-2 w-4 h-4 text-orange-500" />
              123 Main Street, Cape Town, SA
            </p>
            <p className="text-gray-600 mb-2 flex items-center">
              <Mail className="mr-2 w-4 h-4 text-orange-500" />
              <EMAIL>
            </p>
            <p className="text-gray-600 flex items-center">
              <Phone className="mr-2 w-4 h-4 text-orange-500" />
              +27 (0) 21 123 4567
            </p>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-bold text-gray-800 mb-4">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <a href="#" className="text-gray-500 hover:text-orange-600 transition-colors">
                  About Us
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-500 hover:text-orange-600 transition-colors">
                  Services
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-500 hover:text-orange-600 transition-colors">
                  Contact
                </a>
              </li>
            </ul>
          </div>

          {/* Our Services */}
          <div>
            <h3 className="text-lg font-bold text-gray-800 mb-4">Our Services</h3>
            <ul className="space-y-2">
              <li>
                <a href="#" className="text-gray-500 hover:text-orange-600 transition-colors">
                  Web Development
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-500 hover:text-orange-600 transition-colors">
                  Mobile App Development
                </a>
              </li>
              <li>
                <a href="#" className="text-gray-500 hover:text-orange-600 transition-colors">
                  Digital Marketing
                </a>
              </li>
            </ul>
          </div>
        
        {/* Subscribe */}
          <div>
            <h3 className="text-lg font-bold text-gray-800 mb-4">Subscribe</h3>
            <p className="text-gray-600 mb-4">
              Stay up to date with our latest news and offers.
            </p>
            <div className="flex">
              <input
                type="email"
                placeholder="Your email address"
                className="border border-gray-300 rounded-l-md px-4 py-2 focus:outline-none focus:border-orange-500"
              />
              <button className="bg-orange-500 hover:bg-orange-600 text-white px-6 py-2 rounded-r-md transition-colors">
                Subscribe
              </button>
            </div>
          </div>
        </div>
        
        <div className="border-t border-orange-200 pt-8 mt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-600 mb-4 md:mb-0">
              © 2024 South Safari. All rights reserved.
            </p>
            <div className="flex space-x-6">
              <a href="/admin" className="text-sm text-gray-500 hover:text-orange-600 transition-colors">
                Admin Login
              </a>
              <a href="#" className="text-gray-400 hover:text-orange-600 transition-colors">
                Privacy Policy
              </a>
              <a href="#" className="text-gray-400 hover:text-orange-600 transition-colors">
                Terms of Service
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
